<div class="row">
    <div class="col-12">
        <!-- 头部统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><%= tenants.length %></h3>
                        <p>总租户数</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><%= tenants.filter(t => t.status === 'active').length %></h3>
                        <p>活跃租户</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><%= tenants.reduce((sum, t) => sum + parseInt(t.user_count), 0) %></h3>
                        <p>总用户数</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>¥<%= tenants.reduce((sum, t) => sum + parseFloat(t.total_revenue || 0), 0).toLocaleString() %></h3>
                        <p>总收入</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 租户列表卡片 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        租户管理
                    </h5>
                    <div class="btn-group">
                        <a href="/tenants/create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            创建租户
                        </a>
                        <a href="/tenants/subscriptions" class="btn btn-warning">
                            <i class="fas fa-calendar-check me-1"></i>
                            订阅管理
                        </a>
                        <a href="/tenants/usage" class="btn btn-info">
                            <i class="fas fa-chart-bar me-1"></i>
                            使用统计
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <% if (tenants && tenants.length > 0) { %>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>租户信息</th>
                                    <th>类型</th>
                                    <th>订阅计划</th>
                                    <th>用户数</th>
                                    <th>鹅群数</th>
                                    <th>收入</th>
                                    <th>状态</th>
                                    <th>订阅到期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% tenants.forEach(function(tenant) { %>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><%= tenant.tenant_name %></strong>
                                                <br>
                                                <small class="text-muted">
                                                    代码: <%= tenant.tenant_code %>
                                                    <br>
                                                    联系人: <%= tenant.contact_person %>
                                                    <br>
                                                    电话: <%= tenant.contact_phone %>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (tenant.tenant_type === 'individual') { %>
                                                <span class="badge bg-primary">个人</span>
                                            <% } else if (tenant.tenant_type === 'enterprise') { %>
                                                <span class="badge bg-success">企业</span>
                                            <% } else if (tenant.tenant_type === 'cooperative') { %>
                                                <span class="badge bg-info">合作社</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (tenant.subscription_plan === 'basic') { %>
                                                <span class="badge bg-secondary">基础版</span>
                                            <% } else if (tenant.subscription_plan === 'standard') { %>
                                                <span class="badge bg-primary">标准版</span>
                                            <% } else if (tenant.subscription_plan === 'premium') { %>
                                                <span class="badge bg-warning">高级版</span>
                                            <% } else if (tenant.subscription_plan === 'enterprise') { %>
                                                <span class="badge bg-success">企业版</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><%= tenant.user_count %></span>
                                            <small class="text-muted">/ <%= tenant.max_users %></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><%= tenant.flock_count %></span>
                                            <small class="text-muted">/ <%= tenant.max_flocks %></small>
                                        </td>
                                        <td>
                                            <strong>¥<%= (parseFloat(tenant.total_revenue) || 0).toLocaleString() %></strong>
                                        </td>
                                        <td>
                                            <% if (tenant.status === 'active') { %>
                                                <span class="badge bg-success">活跃</span>
                                            <% } else if (tenant.status === 'suspended') { %>
                                                <span class="badge bg-warning">暂停</span>
                                            <% } else { %>
                                                <span class="badge bg-danger">停用</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (tenant.subscription_end) { %>
                                                <%= new Date(tenant.subscription_end).toLocaleDateString() %>
                                                <% 
                                                const today = new Date();
                                                const endDate = new Date(tenant.subscription_end);
                                                const diffTime = endDate.getTime() - today.getTime();
                                                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                                                %>
                                                <br>
                                                <% if (diffDays < 0) { %>
                                                    <small class="text-danger">已过期 <%= Math.abs(diffDays) %> 天</small>
                                                <% } else if (diffDays <= 30) { %>
                                                    <small class="text-warning">还剩 <%= diffDays %> 天</small>
                                                <% } else { %>
                                                    <small class="text-success">还剩 <%= diffDays %> 天</small>
                                                <% } %>
                                            <% } else { %>
                                                <span class="text-muted">无期限</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/tenants/<%= tenant.id %>/details" class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/tenants/<%= tenant.id %>/edit" class="btn btn-sm btn-warning" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-4x text-muted mb-3"></i>
                        <h4>暂无租户</h4>
                        <p class="text-muted">还没有创建任何租户</p>
                        <a href="/tenants/create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            创建第一个租户
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- 成功提示 -->
<% if (typeof success !== 'undefined') { %>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let message = '';
            <% if (success === 'created') { %>
                message = '租户创建成功！';
            <% } else if (success === 'updated') { %>
                message = '租户更新成功！';
            <% } %>
            
            if (message) {
                // 创建成功提示
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-bg-success border-0 position-fixed top-0 end-0 m-3';
                toast.style.zIndex = '9999';
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
                document.body.appendChild(toast);
                
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                toast.addEventListener('hidden.bs.toast', function () {
                    document.body.removeChild(toast);
                });
            }
        });
    </script>
<% } %>