<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    商品管理
                </h5>
                <div class="card-tools">
                    <a href="/mall/products/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        添加商品
                    </a>
                </div>
            </div>
            <div class="card-body">
                <% if (products && products.length > 0) { %>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>商品信息</th>
                                    <th>分类</th>
                                    <th>价格</th>
                                    <th>库存</th>
                                    <th>状态</th>
                                    <th>销量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% products.forEach(function(product) { %>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><%= product.name %></strong>
                                                <br>
                                                <small class="text-muted">
                                                    代码: <%= product.product_code %>
                                                    <br>
                                                    品牌: <%= product.brand || '无' %>
                                                </small>
                                            </div>
                                        </td>
                                        <td><%= product.category_name || '未分类' %></td>
                                        <td>
                                            <strong>¥<%= parseFloat(product.price).toFixed(2) %></strong>
                                            <% if (product.market_price > product.price) { %>
                                                <br>
                                                <small class="text-muted">市场价: ¥<%= parseFloat(product.market_price).toFixed(2) %></small>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (product.stock_qty <= 10) { %>
                                                <span class="badge bg-danger"><%= product.stock_qty %></span>
                                            <% } else if (product.stock_qty <= 50) { %>
                                                <span class="badge bg-warning"><%= product.stock_qty %></span>
                                            <% } else { %>
                                                <span class="badge bg-success"><%= product.stock_qty %></span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (product.status === 'active') { %>
                                                <span class="badge bg-success">上架</span>
                                            <% } else if (product.status === 'inactive') { %>
                                                <span class="badge bg-secondary">下架</span>
                                            <% } else { %>
                                                <span class="badge bg-danger">缺货</span>
                                            <% } %>
                                        </td>
                                        <td><%= product.sales_count || 0 %></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/mall/products/<%= product.id %>/edit" class="btn btn-sm btn-warning" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="/mall/products/<%= product.id %>/view" class="btn btn-sm btn-info" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                        <h4>暂无商品</h4>
                        <p class="text-muted">还没有添加任何商品</p>
                        <a href="/mall/products/create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            添加第一个商品
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>