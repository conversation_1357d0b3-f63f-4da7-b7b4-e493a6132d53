<div class="row">
    <!-- Search and Filters -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    用户管理
                </h5>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form class="row g-3 mb-4" method="GET">
                    <div class="col-md-4">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="搜索用户名、姓名、邮箱、养殖场..." 
                                   value="<%= filters.search %>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="role">
                            <option value="">全部角色</option>
                            <option value="admin" <%= filters.role === 'admin' ? 'selected' : '' %>>管理员</option>
                            <option value="manager" <%= filters.role === 'manager' ? 'selected' : '' %>>管理者</option>
                            <option value="user" <%= filters.role === 'user' ? 'selected' : '' %>>用户</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="active" <%= filters.status === 'active' ? 'selected' : '' %>>正常</option>
                            <option value="inactive" <%= filters.status === 'inactive' ? 'selected' : '' %>>停用</option>
                            <option value="suspended" <%= filters.status === 'suspended' ? 'selected' : '' %>>暂停</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus"></i> 添加用户
                        </button>
                    </div>
                </form>
                
                <!-- Users Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>邮箱</th>
                                <th>养殖场</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>最后登录</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (users && users.length > 0) { %>
                                <% users.forEach(user => { %>
                                    <tr>
                                        <td><%= user.id %></td>
                                        <td>
                                            <strong><%= user.username %></strong>
                                        </td>
                                        <td><%= user.name || '-' %></td>
                                        <td><%= user.email %></td>
                                        <td><%= user.farmName || '-' %></td>
                                        <td>
                                            <span class="badge bg-<%= user.role === 'admin' ? 'danger' : (user.role === 'manager' ? 'warning' : 'primary') %>">
                                                <%= user.role === 'admin' ? '管理员' : (user.role === 'manager' ? '管理者' : '用户') %>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge status-<%= user.status %>">
                                                <%= user.status === 'active' ? '正常' : (user.status === 'inactive' ? '停用' : '暂停') %>
                                            </span>
                                        </td>
                                        <td>
                                            <%= user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未登录' %>
                                        </td>
                                        <td><%= new Date(user.createdAt).toLocaleDateString('zh-CN') %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editUser(<%= user.id %>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <% if (user.role !== 'admin') { %>
                                                <button class="btn btn-outline-danger" onclick="deleteUser(<%= user.id %>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <% } %>
                                            </div>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } else { %>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                        暂无用户数据
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <% if (pagination && pagination.totalPages > 1) { %>
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        <% if (pagination.hasPrev) { %>
                        <li class="page-item">
                            <a class="page-link" href="?page=<%= pagination.page - 1 %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <% } %>
                        
                        <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                        <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                            <a class="page-link" href="?page=<%= i %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                                <%= i %>
                            </a>
                        </li>
                        <% } %>
                        
                        <% if (pagination.hasNext) { %>
                        <li class="page-item">
                            <a class="page-link" href="?page=<%= pagination.page + 1 %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <% } %>
                    </ul>
                </nav>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" name="name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-select" name="role">
                                <option value="user">用户</option>
                                <option value="manager">管理者</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">养殖场名称</label>
                            <input type="text" class="form-control" name="farmName">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Add user form submission
document.getElementById('addUserForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = Utils.getFormData(this);
    
    try {
        Utils.showLoading('正在创建用户...');
        const response = await axios.post('/api/users/create', formData);
        
        if (response.data.success) {
            Utils.showToast('用户创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            setTimeout(() => window.location.reload(), 1000);
        }
    } catch (error) {
        console.error('Create user error:', error);
    } finally {
        Utils.hideLoading();
    }
});

// Edit user function
async function editUser(userId) {
    Utils.showToast('编辑功能开发中', 'info');
}

// Delete user function
async function deleteUser(userId) {
    Utils.confirm('确定要删除这个用户吗？此操作不可恢复。', async () => {
        try {
            Utils.showLoading('正在删除用户...');
            const response = await axios.delete(`/api/users/${userId}`);
            
            if (response.data.success) {
                Utils.showToast('用户删除成功', 'success');
                setTimeout(() => window.location.reload(), 1000);
            }
        } catch (error) {
            console.error('Delete user error:', error);
        } finally {
            Utils.hideLoading();
        }
    });
}
</script>